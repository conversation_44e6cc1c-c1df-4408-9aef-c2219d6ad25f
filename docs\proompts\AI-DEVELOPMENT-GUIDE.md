# 房联（Funi）移动端前端项目 - AI开发指南

## 项目概述

这是一个基于 Vue 3 + Vite + Vant 的移动端前端项目，采用现代化的前端技术栈，专为房联业务场景设计。

## 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 7.0.3
- **UI 组件库**: Vant 4.9.20 (移动端 UI 库)
- **路由**: Vue Router 4.5.1 + unplugin-vue-router (文件系统路由)
- **状态管理**: Pinia 3.0.3 + pinia-plugin-persistedstate (持久化)
- **样式**: SCSS + PostCSS (移动端适配)
- **HTTP 请求**: @funi-lib/utils (房联自研工具库)
- **包管理**: pnpm

## 项目结构

```
src/
├── api/                    # API 接口定义
├── assets/                 # 静态资源
├── components/             # 公共组件
│   └── NavBar.vue         # 导航栏组件
├── pages/                  # 页面组件 (文件系统路由)
│   ├── home/              # 首页模块
│   └── mine/              # 我的模块
├── router/                 # 路由配置
├── stores/                 # Pinia 状态管理
│   ├── index.js           # Store 入口
│   └── modules/           # Store 模块
├── utils/                  # 工具函数
│   └── http/              # HTTP 请求封装
├── App.vue                # 根组件
├── main.js                # 应用入口
└── style.css              # 全局样式
```

## 开发规范

### 1. 页面组件开发

#### 文件结构
app.vue已包含导航栏,每个页面组件不需要单独添加导航栏，页面必须包含以下结构：

```vue
<template>
  <!-- 页面内容 -->
</template>

<script setup>
// 使用 Composition API
</script>

<style lang="scss" scoped>
// 样式使用 SCSS，必须添加 scoped
</style>

<route lang="json5">
{
  name: 'RouteName',
  meta: {
    title: '页面标题'
  }
}
</route>
```

#### 路由配置
- 使用文件系统路由，页面文件放在 `src/pages/` 目录下
- 路由配置通过 `<route>` 块定义
- `components/` 和 `common/` 文件夹会被自动排除
- `name` 对应为路由的name
- 路由元信息中的 `title` 会被导航栏自动使用

### 2. 组件开发

#### UI 组件
- 优先使用 Vant UI 组件库
- 组件名使用 `Van` 前缀，如 `VanButton`、`VanNavBar`
- 全局已注册 Vant 组件，可直接使用

#### 自定义组件
- 放在 `src/components/` 目录下
- 使用 PascalCase 命名

### 3. 状态管理

#### Pinia Store
```javascript
import { defineStore } from 'pinia'

const useExampleStore = defineStore('example', () => {
  // 状态
  const state = ref(initialValue)
  
  // 计算属性
  const computed = computed(() => state.value * 2)
  
  // 方法
  function updateState(newValue) {
    state.value = newValue
  }
  
  return {
    state,
    computed,
    updateState
  }
}, {
  persist: true // 启用持久化
})
```

#### 使用 Store
```javascript
import { useExampleStore } from '@/stores/modules/example'

const store = useExampleStore()
```

### 4. HTTP 请求

#### 基础用法
```javascript
// 全局 HTTP 实例已挂载到 window.$http
const response = await window.$http.post('/api/endpoint', data)
const response = await window.$http.fetch('/api/endpoint', params)
```

#### 特性
- 自动处理加密/解密
- 统一错误处理
- 自动添加认证头
- 支持文件上传下载

### 5. 样式开发

#### 移动端适配
- 使用 `postcss-mobile-forever` 插件自动适配
- 设计稿基准：375px
- 最大显示宽度：600px
- 自动添加边框效果

#### SCSS 使用
```scss
.example {
  // 使用相对单位，插件会自动转换
  width: 100px;
  height: 50px;
  
  // 嵌套语法
  &__item {
    padding: 10px;
  }
}
```

### 6. 路径别名

```javascript
// 可用的路径别名
import Component from '@/components/Component.vue'  // src/
import image from '~/image.png'                    // src/assets/
import config from '~root/config.js'               // 项目根目录
```

## 常用开发模式

### 1. 页面开发模板

```vue
<template>
  <div class="page-container">
   <!-- 页面内容 -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const data = ref([])

// 方法
const onBack = () => {
  router.back()
}

const fetchData = async () => {
  loading.value = true
  try {
    const response = await window.$http.fetch('/api/data')
    data.value = response.data
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 16px;
}
</style>

<route lang="json5">
{
  name: 'ExamplePage',
  meta: {
    title: '示例页面'
  }
}
</route>
```

### 2. 列表页面模板

```vue
<template>
  <div class="list-page">
    <VanPullRefresh v-model="refreshing" @refresh="onRefresh">
      <VanList
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <VanCell
          v-for="item in list"
          :key="item.id"
          :title="item.title"
          :value="item.value"
          is-link
          @click="onItemClick(item)"
        />
      </VanList>
    </VanPullRefresh>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const list = ref([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const page = ref(1)

const onLoad = async () => {
  try {
    const response = await window.$http.fetch('/api/list', {
      page: page.value,
      size: 20
    })
    
    if (page.value === 1) {
      list.value = response.data
    } else {
      list.value.push(...response.data)
    }
    
    page.value++
    loading.value = false
    
    if (response.data.length < 20) {
      finished.value = true
    }
  } catch (error) {
    loading.value = false
  }
}

const onRefresh = async () => {
  page.value = 1
  finished.value = false
  await onLoad()
  refreshing.value = false
}

const onItemClick = (item) => {
  router.push(`/detail/${item.id}`)
}
</script>
</route>
```

## 注意事项

1. **组件库优先级**: 优先使用 Vant UI 组件，避免重复造轮子
2. **响应式设计**: 所有尺寸使用 px，构建工具会自动转换为适配单位
3. **错误处理**: HTTP 请求已有统一错误处理，特殊情况可自定义
4. **性能优化**: 大列表使用虚拟滚动，图片使用懒加载
5. **代码规范**: 使用 Prettier 保持代码风格一致

## 启动和构建

```bash
# 安装依赖
pnpm install

# 开发环境启动
pnpm dev

# 生产环境构建
pnpm build

# 预览构建结果
pnpm preview
```

## 调试和开发工具

- 开发服务器端口: 8000
- 支持热更新
- Vue DevTools 支持
- 移动端调试: 使用浏览器开发者工具的设备模拟器

## API 开发规范

### 1. API 接口定义

在 `src/api/` 目录下按模块组织 API 接口：

```javascript
// src/api/user.js
export const userApi = {
  // 获取用户信息
  getUserInfo: (params) => window.$http.fetch('/api/user/info', params),

  // 更新用户信息
  updateUserInfo: (data) => window.$http.post('/api/user/update', data),

  // 上传头像
  uploadAvatar: (formData) => window.$http.upload2('/api/user/avatar', formData)
}
```

### 2. 错误处理

HTTP 请求已内置统一错误处理，包括：
- 认证失败自动跳转登录
- 网络错误提示
- 业务错误弹窗显示

特殊情况可自定义处理：

```javascript
try {
  const response = await window.$http.post('/api/endpoint', data)
  // 处理成功响应
} catch (error) {
  // 自定义错误处理
  console.error('请求失败:', error)
}
```

## 常用 Vant 组件使用示例

### 1. 表单组件

```vue
<template>
  <VanForm @submit="onSubmit">
    <VanCellGroup inset>
      <VanField
        v-model="form.name"
        name="name"
        label="姓名"
        placeholder="请输入姓名"
        :rules="[{ required: true, message: '请填写姓名' }]"
      />
      <VanField
        v-model="form.phone"
        name="phone"
        label="手机号"
        placeholder="请输入手机号"
        :rules="[{ required: true, message: '请填写手机号' }]"
      />
    </VanCellGroup>
    <div class="submit-btn">
      <VanButton round block type="primary" native-type="submit">
        提交
      </VanButton>
    </div>
  </VanForm>
</template>

<script setup>
import { ref } from 'vue'
import { showToast } from 'vant'

const form = ref({
  name: '',
  phone: ''
})

const onSubmit = async (values) => {
  try {
    await window.$http.post('/api/submit', values)
    showToast('提交成功')
  } catch (error) {
    showToast('提交失败')
  }
}
</script>
```

### 2. 弹窗组件

```vue
<template>
  <div>
    <VanButton @click="showDialog">显示弹窗</VanButton>

    <!-- 确认弹窗 -->
    <VanDialog
      v-model:show="dialogVisible"
      title="确认删除"
      message="删除后无法恢复，确定要删除吗？"
      show-cancel-button
      @confirm="onConfirm"
      @cancel="onCancel"
    />

    <!-- 底部弹出层 -->
    <VanPopup
      v-model:show="popupVisible"
      position="bottom"
      :style="{ height: '30%' }"
    >
      <div class="popup-content">
        弹出层内容
      </div>
    </VanPopup>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { showToast } from 'vant'

const dialogVisible = ref(false)
const popupVisible = ref(false)

const showDialog = () => {
  dialogVisible.value = true
}

const onConfirm = () => {
  showToast('确认删除')
  dialogVisible.value = false
}

const onCancel = () => {
  dialogVisible.value = false
}
</script>
```

### 3. 选择器组件

```vue
<template>
  <div>
    <VanField
      v-model="selectedDate"
      readonly
      clickable
      label="选择日期"
      placeholder="点击选择日期"
      @click="showDatePicker = true"
    />

    <VanPopup v-model:show="showDatePicker" position="bottom">
      <VanDatePicker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </VanPopup>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const showDatePicker = ref(false)
const currentDate = ref(['2024', '01', '01'])
const selectedDate = ref('')

const onDateConfirm = ({ selectedValues }) => {
  selectedDate.value = selectedValues.join('-')
  showDatePicker.value = false
}
</script>
```

## 移动端开发最佳实践

### 1. 触摸交互

```vue
<template>
  <div
    class="touch-area"
    @touchstart="onTouchStart"
    @touchmove="onTouchMove"
    @touchend="onTouchEnd"
  >
    触摸区域
  </div>
</template>

<script setup>
const onTouchStart = (event) => {
  // 触摸开始
}

const onTouchMove = (event) => {
  // 触摸移动
  event.preventDefault() // 阻止默认滚动
}

const onTouchEnd = (event) => {
  // 触摸结束
}
</script>
```

### 2. 安全区域适配

```scss
.safe-area {
  // 适配刘海屏等异形屏
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}
```

### 3. 性能优化

```vue
<script setup>
import { ref, computed, nextTick } from 'vue'

// 使用 computed 缓存计算结果
const expensiveValue = computed(() => {
  return heavyCalculation(someReactiveValue.value)
})

// 使用 nextTick 确保 DOM 更新完成
const updateDOM = async () => {
  someValue.value = newValue
  await nextTick()
  // DOM 已更新，可以安全操作
}

// 防抖处理
import { debounce } from 'lodash-es'
const debouncedSearch = debounce((keyword) => {
  // 搜索逻辑
}, 300)
</script>
```

## 常见问题解决方案

### 1. 路由跳转

```javascript
import { useRouter } from 'vue-router'

const router = useRouter()

// 页面跳转
router.push('/path')
router.push({ name: 'PageName', params: { id: 1 } })

// 替换当前页面
router.replace('/path')

// 返回上一页
router.back()
router.go(-1)
```

### 2. 数据持久化

```javascript
// 使用 Pinia 持久化
const useUserStore = defineStore('user', () => {
  const userInfo = ref({})

  return { userInfo }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage, // 或 sessionStorage
    paths: ['userInfo'] // 指定持久化的字段
  }
})
```

### 3. 环境变量

```javascript
// 判断环境
import FuniJS from '@funi-lib/utils'

if (FuniJS.isProduction()) {
  // 生产环境
} else {
  // 开发环境
}
```

## 代码提交规范

### Commit Message 格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Type 类型

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构代码
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例

```
feat(user): 添加用户头像上传功能

- 新增头像上传组件
- 集成图片裁剪功能
- 添加上传进度显示

Closes #123
```
